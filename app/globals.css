@import "tailwindcss";

:root {
  /* McMaster-Carr clean color palette */
  --background: #ffffff;
  --foreground: #000000;
  --primary: #0066cc;
  --primary-hover: #0052a3;
  --secondary: #666666;
  --border: #cccccc;
  --muted: #f5f5f5;
  --muted-foreground: #666666;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-border: var(--border);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --font-sans: var(--font-inter);
}

/* Light mode (default) */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
}

/* Typography improvements */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  line-height: 1.2;
  color: var(--foreground);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}