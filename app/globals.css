@import "tailwindcss";

:root {
  /* McMaster-Carr inspired color palette */
  --background: #ffffff;
  --foreground: #1f2937;
  --primary: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary: #6b7280;
  --accent: #f59e0b;
  --border: #e5e7eb;
  --muted: #f9fafb;
  --muted-foreground: #6b7280;
  --destructive: #dc2626;
  --success: #059669;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-border: var(--border);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-destructive: var(--destructive);
  --color-success: var(--success);
  --font-sans: var(--font-roboto-sans);
  --font-mono: var(--font-roboto-mono);
}

/* Light mode (default) */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-roboto-sans), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
}

/* Typography improvements */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  line-height: 1.2;
  color: var(--foreground);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}