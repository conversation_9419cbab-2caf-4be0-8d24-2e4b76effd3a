import { layout as Layout } from '@/ui/components';
import Link from 'next/link';

export default function Page() {
  const tools = [
    {
      title: 'Gear Calculator',
      description: 'Calculate gear ratios, pitch diameters, and center distances for mechanical gear systems.',
      href: '/calculators/gear',
      icon: 'ICON'
    },
    {
      title: 'Windings Calculator',
      description: 'Motor and transformer winding calculations for electrical machine design.',
      href: '/calculators/windings',
      icon: 'ICON'
    },
    {
      title: 'Material Properties',
      description: 'Lookup mechanical properties of common engineering materials and alloys.',
      href: '/tools/materials',
      icon: 'ICON'
    },
    {
      title: 'Unit Converter',
      description: 'Convert between metric and imperial units for engineering calculations.',
      href: '/tools/converter',
      icon: 'ICON'
    }
  ];

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-semibold text-black mb-2">Engineering Tools</h1>
          <p className="text-gray-700">Clean, fast tools for mechanical engineering calculations.</p>
        </div>

        {/* Tools List */}
        <div className="grid grid-cols-2 gap-2">
          {tools.map((tool) => (
            <Link key={tool.title} href={tool.href} className="block">
              <div className="border border-gray-300 bg-white hover:bg-gray-50">
                <div className="p-4 flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-gray-100 border border-gray-300 flex items-center justify-center text-lg">
                    {tool.icon}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-medium text-black mb-1">{tool.title}</h3>
                    <p className="text-sm text-gray-600 leading-relaxed">{tool.description}</p>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </Layout>
  );
}