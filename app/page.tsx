import { layout as Layout } from '@/ui/components';
import { button as <PERSON><PERSON>, card as Card, card<PERSON>eader as <PERSON><PERSON><PERSON><PERSON>, cardContent as <PERSON><PERSON>ontent } from '@/ui/primitives';
import Link from 'next/link';

export default function Page() {
  const toolCategories = [
    {
      title: 'Calculators',
      description: 'Precision engineering calculations for mechanical design',
      tools: [
        { name: 'Gear Calculator', href: '/calculators/gear', description: 'Calculate gear ratios, dimensions, and specifications' },
        { name: 'Windings Calculator', href: '/calculators/windings', description: 'Motor and transformer winding calculations' },
      ]
    },
    {
      title: 'Material Properties',
      description: 'Comprehensive material database and property lookup',
      tools: [
        { name: 'Steel Properties', href: '/materials/steel', description: 'Mechanical properties of steel alloys' },
        { name: 'Aluminum Properties', href: '/materials/aluminum', description: 'Properties of aluminum alloys' },
      ]
    },
    {
      title: 'Unit Conversion',
      description: 'Quick and accurate unit conversions for engineering',
      tools: [
        { name: 'Length Converter', href: '/tools/length', description: 'Convert between metric and imperial units' },
        { name: 'Force Converter', href: '/tools/force', description: 'Convert force units and calculations' },
      ]
    }
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-blue-50 to-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Professional Mechanical
              <span className="text-blue-600 block">Engineering Tools</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Quick, functional, and reliable engineering calculators and tools for mechanical engineers,
              designers, and technicians. No bullshit, just results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/calculators">
                <Button variant="primary" size="lg">
                  Browse Calculators
                </Button>
              </Link>
              <Link href="/tools">
                <Button variant="outline" size="lg">
                  Explore Tools
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Tools Grid */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Engineering Tools & Calculators
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Professional-grade tools designed for accuracy, speed, and reliability in mechanical engineering applications.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {toolCategories.map((category) => (
              <card key={category.title} className="h-full">
                <cardHeader>
                  <h3 className="text-xl font-semibold text-gray-900">{category.title}</h3>
                  <p className="text-gray-600">{category.description}</p>
                </cardHeader>
                <cardContent>
                  <div className="space-y-4">
                    {category.tools.map((tool) => (
                      <div key={tool.name} className="border-l-4 border-blue-600 pl-4">
                        <Link href={tool.href} className="block hover:bg-gray-50 p-2 rounded transition-colors">
                          <h4 className="font-medium text-gray-900 hover:text-blue-600">{tool.name}</h4>
                          <p className="text-sm text-gray-600">{tool.description}</p>
                        </Link>
                      </div>
                    ))}
                  </div>
                </cardContent>
              </card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why Choose MechWave?
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Fast & Reliable</h3>
              <p className="text-gray-600">Instant calculations with professional accuracy. No waiting, no complexity.</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Engineering Grade</h3>
              <p className="text-gray-600">Built by engineers, for engineers. Trusted formulas and industry standards.</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Mobile Ready</h3>
              <p className="text-gray-600">Access your tools anywhere. Responsive design for desktop, tablet, and mobile.</p>
            </div>
          </div>
        </div>
      </section>
    </layout>
  );
}