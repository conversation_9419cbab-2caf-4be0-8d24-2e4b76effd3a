import Link from 'next/link';

export default function Page() {
  const tools = [
    {
      title: 'Gear Calculator',
      description: 'Calculate gear ratios, pitch diameters, and center distances for mechanical gear systems.',
      href: '/calculators/gear',
      icon: 'CALC'
    },
    {
      title: 'Windings Calculator',
      description: 'Motor and transformer winding calculations for electrical machine design.',
      href: '/calculators/windings',
      icon: 'WIND'
    },
    {
      title: 'Material Properties',
      description: 'Lookup mechanical properties of common engineering materials and alloys.',
      href: '/tools/materials',
      icon: 'MAT'
    },
    {
      title: 'Unit Converter',
      description: 'Convert between metric and imperial units for engineering calculations.',
      href: '/tools/converter',
      icon: 'CONV'
    }
  ];

  return (
    <div className="max-w-5xl mx-auto px-6 py-12">
      {/* Header */}
      <div className="mb-12">
        <h1 className="text-3xl font-medium text-black mb-4">Engineering Tools</h1>
        <p className="text-lg text-gray-600 max-w-2xl">
          Professional engineering calculators and reference tools. Clean, fast, and reliable calculations for mechanical engineers.
        </p>
      </div>

      {/* Tools Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {tools.map((tool) => (
          <Link key={tool.title} href={tool.href} className="block group">
            <div className="border border-gray-200 bg-white hover:border-gray-300">
              <div className="p-6 flex items-start space-x-4">
                {/* Icon */}
                <div className="flex-shrink-0 w-16 h-16 bg-gray-50 border border-gray-200 flex items-center justify-center">
                  <span className="text-xs font-medium text-gray-600">{tool.icon}</span>
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <h3 className="text-base  mb-1 group-hover:text-blue-600">
                    {tool.title}
                  </h3>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    {tool.description}
                  </p>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Additional Info */}
      <div className="mt-16 pt-8 border-t border-gray-200">
        <div className="text-center">
          <h2 className="text-xl font-medium text-black mb-4">About These Tools</h2>
          <p className="text-gray-600 max-w-3xl mx-auto">
            These engineering tools are designed for accuracy and speed. All calculations use standard engineering formulas
            and are suitable for professional use. Results should be verified for critical applications.
          </p>
        </div>
      </div>
    </div>
  );
}