import { layout as Layout } from '@/ui/components';
import Link from 'next/link';

export default function CalculatorsPage() {
  const calculators = [
    {
      name: 'Gear Calculator',
      href: '/calculators/gear',
      description: 'Calculate gear ratios, pitch diameters, and center distances'
    },
    {
      name: 'Windings Calculator',
      href: '/calculators/windings',
      description: 'Motor and transformer winding calculations'
    }
  ];

  return (
    <Layout>
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-semibold text-black mb-2">Calculators</h1>
          <p className="text-gray-700">Engineering calculation tools for mechanical design.</p>
        </div>

        <div className="border border-gray-300 bg-white">
          <div className="bg-gray-100 px-4 py-3 border-b border-gray-300">
            <h2 className="text-lg font-semibold text-black">Available Calculators</h2>
          </div>
          <div className="p-4">
            <div className="space-y-4">
              {calculators.map((calc) => (
                <div key={calc.name} className="border-b border-gray-200 pb-4 last:border-b-0 last:pb-0">
                  <Link href={calc.href} className="block hover:bg-gray-50 p-2 -m-2">
                    <h3 className="text-blue-700 hover:text-blue-900 hover:underline font-medium">
                      {calc.name}
                    </h3>
                    <p className="text-sm text-gray-600 mt-1">{calc.description}</p>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
