import Link from 'next/link';

export default function CalculatorsPage() {
  const calculators = [
    {
      name: 'Gear Calculator',
      href: '/calculators/gear',
      description: 'Calculate gear ratios, pitch diameters, and center distances'
    },
    {
      name: 'Windings Calculator',
      href: '/calculators/windings',
      description: 'Motor and transformer winding calculations'
    }
  ];

  return (
    <div className="max-w-5xl mx-auto px-6 py-12">
      <div className="mb-8">
        <h1 className="text-3xl font-medium text-black mb-4">Calculators</h1>
        <p className="text-lg text-gray-600">Engineering calculation tools for mechanical design.</p>
      </div>

      <div className="border border-gray-200 bg-white">
        <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-medium text-black">Available Calculators</h2>
        </div>
        <div className="p-6">
          <div className="space-y-6">
            {calculators.map((calc) => (
              <div key={calc.name} className="border-b border-gray-100 pb-6 last:border-b-0 last:pb-0">
                <Link href={calc.href} className="block hover:bg-gray-50 p-4 -m-4 rounded">
                  <h3 className="text-xl font-medium text-black hover:text-blue-600 transition-colors mb-2">
                    {calc.name}
                  </h3>
                  <p className="text-gray-600">{calc.description}</p>
                </Link>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
