'use client';

import { layout as Layout } from '@/ui/components';
import { input as Input } from '@/ui/primitives';
import { useState } from 'react';

export default function GearCalculatorPage() {
  const [inputs, setInputs] = useState({
    teeth1: '',
    teeth2: '',
    module: '',
    pressureAngle: '20'
  });

  const [results, setResults] = useState({
    ratio: 0,
    pitchDiameter1: 0,
    pitchDiameter2: 0,
    centerDistance: 0
  });

  const handleInputChange = (field: string, value: string) => {
    const newInputs = { ...inputs, [field]: value };
    setInputs(newInputs);
    calculateResults(newInputs);
  };

  const calculateResults = (inputValues: typeof inputs) => {
    const t1 = parseFloat(inputValues.teeth1) || 0;
    const t2 = parseFloat(inputValues.teeth2) || 0;
    const m = parseFloat(inputValues.module) || 0;

    if (t1 > 0 && t2 > 0 && m > 0) {
      const ratio = t2 / t1;
      const pd1 = t1 * m;
      const pd2 = t2 * m;
      const centerDist = (pd1 + pd2) / 2;

      setResults({
        ratio: ratio,
        pitchDiameter1: pd1,
        pitchDiameter2: pd2,
        centerDistance: centerDist
      });
    } else {
      setResults({
        ratio: 0,
        pitchDiameter1: 0,
        pitchDiameter2: 0,
        centerDistance: 0
      });
    }
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-semibold text-black mb-2">Gear Calculator</h1>
          <p className="text-gray-700">Calculate gear ratios, pitch diameters, and center distances.</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Input Section */}
          <div className="border border-gray-300 bg-white">
            <div className="bg-gray-100 px-4 py-3 border-b border-gray-300">
              <h2 className="text-lg font-semibold text-black">Input Parameters</h2>
            </div>
            <div className="p-4 space-y-4">
              <Input
                label="Number of Teeth (Pinion)"
                type="number"
                value={inputs.teeth1}
                onChange={(e) => handleInputChange('teeth1', e.target.value)}
                placeholder="Enter number of teeth"
              />
              <Input
                label="Number of Teeth (Gear)"
                type="number"
                value={inputs.teeth2}
                onChange={(e) => handleInputChange('teeth2', e.target.value)}
                placeholder="Enter number of teeth"
              />
              <Input
                label="Module (mm)"
                type="number"
                step="0.1"
                value={inputs.module}
                onChange={(e) => handleInputChange('module', e.target.value)}
                placeholder="Enter module"
              />
              <Input
                label="Pressure Angle (degrees)"
                type="number"
                value={inputs.pressureAngle}
                onChange={(e) => handleInputChange('pressureAngle', e.target.value)}
                placeholder="20"
              />
            </div>
          </div>

          {/* Results Section */}
          <div className="border border-gray-300 bg-white">
            <div className="bg-gray-100 px-4 py-3 border-b border-gray-300">
              <h2 className="text-lg font-semibold text-black">Results</h2>
            </div>
            <div className="p-4 space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Gear Ratio:</span>
                  <div className="font-mono text-black">{results.ratio.toFixed(3)}</div>
                </div>
                <div>
                  <span className="text-gray-600">Pinion Pitch Diameter:</span>
                  <div className="font-mono text-black">{results.pitchDiameter1.toFixed(2)} mm</div>
                </div>
                <div>
                  <span className="text-gray-600">Gear Pitch Diameter:</span>
                  <div className="font-mono text-black">{results.pitchDiameter2.toFixed(2)} mm</div>
                </div>
                <div>
                  <span className="text-gray-600">Center Distance:</span>
                  <div className="font-mono text-black">{results.centerDistance.toFixed(2)} mm</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Formulas */}
        <div className="mt-6 border border-gray-300 bg-white">
          <div className="bg-gray-100 px-4 py-3 border-b border-gray-300">
            <h2 className="text-lg font-semibold text-black">Formulas Used</h2>
          </div>
          <div className="p-4 text-sm">
            <div className="space-y-2 font-mono text-gray-700">
              <div>Gear Ratio = N₂ / N₁</div>
              <div>Pitch Diameter = Number of Teeth × Module</div>
              <div>Center Distance = (D₁ + D₂) / 2</div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
