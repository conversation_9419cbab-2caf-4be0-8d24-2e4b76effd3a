'use client';

import { layout as Layout } from '@/ui/components';
import { input as Input } from '@/ui/primitives';
import { useState } from 'react';

export default function WindingsCalculatorPage() {
  const [inputs, setInputs] = useState({
    voltage: '',
    frequency: '50',
    poles: '4',
    slots: '',
    conductorsPerSlot: ''
  });

  const [results, setResults] = useState({
    turnsPerPhase: 0,
    turnsPerCoil: 0,
    coilSpan: 0,
    slotsPerPole: 0
  });

  const handleInputChange = (field: string, value: string) => {
    const newInputs = { ...inputs, [field]: value };
    setInputs(newInputs);
    calculateResults(newInputs);
  };

  const calculateResults = (inputValues: typeof inputs) => {
    const V = parseFloat(inputValues.voltage) || 0;
    const f = parseFloat(inputValues.frequency) || 50;
    const p = parseFloat(inputValues.poles) || 4;
    const slots = parseFloat(inputValues.slots) || 0;
    const condPerSlot = parseFloat(inputValues.conductorsPerSlot) || 0;

    if (V > 0 && f > 0 && p > 0 && slots > 0) {
      const slotsPerPole = slots / p;
      const coilSpan = Math.round(slots / p * 0.8); // Typical 80% span
      const turnsPerPhase = Math.round(V / (4.44 * f * 0.1)); // Simplified calculation
      const turnsPerCoil = Math.round(turnsPerPhase / (slots / 6)); // For 3-phase

      setResults({
        turnsPerPhase: turnsPerPhase,
        turnsPerCoil: turnsPerCoil,
        coilSpan: coilSpan,
        slotsPerPole: slotsPerPole
      });
    } else {
      setResults({
        turnsPerPhase: 0,
        turnsPerCoil: 0,
        coilSpan: 0,
        slotsPerPole: 0
      });
    }
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-semibold text-black mb-2">Windings Calculator</h1>
          <p className="text-gray-700">Calculate motor and transformer winding parameters.</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Input Section */}
          <div className="border border-gray-300 bg-white">
            <div className="bg-gray-100 px-4 py-3 border-b border-gray-300">
              <h2 className="text-lg font-semibold text-black">Input Parameters</h2>
            </div>
            <div className="p-4 space-y-4">
              <Input
                label="Voltage (V)"
                type="number"
                value={inputs.voltage}
                onChange={(e) => handleInputChange('voltage', e.target.value)}
                placeholder="Enter voltage"
              />
              <Input
                label="Frequency (Hz)"
                type="number"
                value={inputs.frequency}
                onChange={(e) => handleInputChange('frequency', e.target.value)}
                placeholder="50"
              />
              <Input
                label="Number of Poles"
                type="number"
                value={inputs.poles}
                onChange={(e) => handleInputChange('poles', e.target.value)}
                placeholder="4"
              />
              <Input
                label="Number of Slots"
                type="number"
                value={inputs.slots}
                onChange={(e) => handleInputChange('slots', e.target.value)}
                placeholder="Enter number of slots"
              />
              <Input
                label="Conductors per Slot"
                type="number"
                value={inputs.conductorsPerSlot}
                onChange={(e) => handleInputChange('conductorsPerSlot', e.target.value)}
                placeholder="Enter conductors per slot"
              />
            </div>
          </div>

          {/* Results Section */}
          <div className="border border-gray-300 bg-white">
            <div className="bg-gray-100 px-4 py-3 border-b border-gray-300">
              <h2 className="text-lg font-semibold text-black">Results</h2>
            </div>
            <div className="p-4 space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Turns per Phase:</span>
                  <div className="font-mono text-black">{results.turnsPerPhase}</div>
                </div>
                <div>
                  <span className="text-gray-600">Turns per Coil:</span>
                  <div className="font-mono text-black">{results.turnsPerCoil}</div>
                </div>
                <div>
                  <span className="text-gray-600">Coil Span:</span>
                  <div className="font-mono text-black">{results.coilSpan} slots</div>
                </div>
                <div>
                  <span className="text-gray-600">Slots per Pole:</span>
                  <div className="font-mono text-black">{results.slotsPerPole.toFixed(1)}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Notes */}
        <div className="mt-6 border border-gray-300 bg-white">
          <div className="bg-gray-100 px-4 py-3 border-b border-gray-300">
            <h2 className="text-lg font-semibold text-black">Notes</h2>
          </div>
          <div className="p-4 text-sm text-gray-700">
            <ul className="space-y-1">
              <li>• Calculations are simplified for basic motor design</li>
              <li>• Coil span is typically 80% of pole pitch</li>
              <li>• For precise designs, consult motor design handbooks</li>
              <li>• Results are approximate and for reference only</li>
            </ul>
          </div>
        </div>
      </div>
    </Layout>
  );
}
