import type { <PERSON>ada<PERSON> } from "next";
import { Robot<PERSON> } from "next/font/google";
import "./globals.css";

const robotoSans = Roboto({
  variable: "--font-roboto",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "Mech - Engineering Tools",
  description: "Clean, fast engineering tools and calculators",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${robotoSans.className} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
