import Link from 'next/link';

export default function ToolsPage() {
  const tools = [
    {
      name: 'Material Properties',
      href: '/tools/materials',
      description: 'Lookup mechanical properties of common engineering materials'
    },
    {
      name: 'Unit Converter',
      href: '/tools/converter',
      description: 'Convert between metric and imperial units'
    }
  ];

  return (
    <div className="max-w-5xl mx-auto px-6 py-12">
      <div className="mb-8">
        <h1 className="text-3xl font-medium text-black mb-4">Tools</h1>
        <p className="text-lg text-gray-600">Reference tools and utilities for mechanical engineering.</p>
      </div>

      <div className="border border-gray-200 bg-white">
        <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-medium text-black">Available Tools</h2>
        </div>
        <div className="p-6">
          <div className="space-y-6">
            {tools.map((tool) => (
              <div key={tool.name} className="border-b border-gray-100 pb-6 last:border-b-0 last:pb-0">
                <Link href={tool.href} className="block hover:bg-gray-50 p-4 -m-4 rounded">
                  <h3 className="text-xl font-medium text-black hover:text-blue-600 transition-colors mb-2">
                    {tool.name}
                  </h3>
                  <p className="text-gray-600">{tool.description}</p>
                </Link>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
