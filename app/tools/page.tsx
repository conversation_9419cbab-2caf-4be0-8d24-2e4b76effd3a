import { layout as Layout } from '@/ui/components';
import Link from 'next/link';

export default function ToolsPage() {
  const tools = [
    {
      name: 'Material Properties',
      href: '/tools/materials',
      description: 'Lookup mechanical properties of common engineering materials'
    },
    {
      name: 'Unit Converter',
      href: '/tools/converter',
      description: 'Convert between metric and imperial units'
    }
  ];

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-semibold text-black mb-2">Tools</h1>
          <p className="text-gray-700">Reference tools and utilities for mechanical engineering.</p>
        </div>

        <div className="border border-gray-300 bg-white">
          <div className="bg-gray-100 px-4 py-3 border-b border-gray-300">
            <h2 className="text-lg font-semibold text-black">Available Tools</h2>
          </div>
          <div className="p-4">
            <div className="space-y-4">
              {tools.map((tool) => (
                <div key={tool.name} className="border-b border-gray-200 pb-4 last:border-b-0 last:pb-0">
                  <Link href={tool.href} className="block hover:bg-gray-50 p-2 -m-2">
                    <h3 className="text-blue-700 hover:text-blue-900 hover:underline font-medium">
                      {tool.name}
                    </h3>
                    <p className="text-sm text-gray-600 mt-1">{tool.description}</p>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
