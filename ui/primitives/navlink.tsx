import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface NavLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  active?: boolean;
  external?: boolean;
}

const navlink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(
  ({ href, children, className, active = false, external = false, ...props }, ref) => {
    const baseStyles = 'inline-flex items-center px-3 py-2 text-sm font-medium transition-colors hover:text-blue-600';
    const activeStyles = active ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-700';
    
    if (external) {
      return (
        <a
          href={href}
          ref={ref}
          className={cn(baseStyles, activeStyles, className)}
          target="_blank"
          rel="noopener noreferrer"
          {...props}
        >
          {children}
        </a>
      );
    }
    
    return (
      <Link
        href={href}
        ref={ref}
        className={cn(baseStyles, activeStyles, className)}
        {...props}
      >
        {children}
      </Link>
    );
  }
);

navlink.displayName = 'NavLink';

export { navlink };
export type { NavLinkProps };
