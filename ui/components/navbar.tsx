'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { navlink } from '@/ui/primitives';

const navbar = () => {
  const pathname = usePathname();

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'Calculators', href: '/calculators' },
    { name: 'Tools', href: '/tools' },
  ];

  return (
    <nav className="bg-white border-b border-gray-300">
      <div className="max-w-4xl mx-auto px-4">
        <div className="flex justify-between items-center h-14">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center">
              <span className="text-lg font-semibold text-black">LOGO</span>
            </Link>
          </div>

          {/* Navigation Links */}
          <div className="flex space-x-6">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`text-sm ${pathname === item.href
                  ? 'text-blue-700 font-semibold'
                  : 'text-gray-700 hover:text-blue-700'
                  }`}
              >
                {item.name}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
};

navbar.displayName = 'Navbar';

export { navbar };
