'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const navbar = () => {
  const pathname = usePathname();

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'Calculators', href: '/calculators' },
    { name: 'Tools', href: '/tools' },
  ];

  return (
    <nav className="bg-white border-b border-gray-200">
      <div className="max-w-5xl mx-auto px-6">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="text-xl font-medium text-black">
              LOGO
            </Link>
          </div>

          {/* Navigation Links */}
          <div className="flex space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`text-sm font-medium ${pathname === item.href
                    ? 'text-black'
                    : 'text-gray-600 hover:text-black'
                  }`}
              >
                {item.name}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
};

navbar.displayName = 'Navbar';

export { navbar };
