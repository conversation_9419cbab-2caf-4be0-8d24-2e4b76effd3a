import React from 'react';
import Link from 'next/link';

const footer = () => {
  const currentYear = new Date().getFullYear();
  
  const footerLinks = {
    Tools: [
      { name: 'Gear Calculator', href: '/calculators/gear' },
      { name: 'Windings Calculator', href: '/calculators/windings' },
      { name: 'Material Properties', href: '/tools/materials' },
      { name: 'Unit Converter', href: '/tools/converter' },
    ],
    Resources: [
      { name: 'Documentation', href: '/docs' },
      { name: 'Tutorials', href: '/tutorials' },
      { name: 'Standards', href: '/standards' },
      { name: 'FAQ', href: '/faq' },
    ],
    Company: [
      { name: 'About', href: '/about' },
      { name: 'Contact', href: '/contact' },
      { name: 'Privacy', href: '/privacy' },
      { name: 'Terms', href: '/terms' },
    ],
  };
  
  return (
    <footer className="bg-gray-50 border-t border-gray-200">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-blue-600 rounded-md flex items-center justify-center">
                <span className="text-white font-bold text-lg">M</span>
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900">MechWave</span>
            </div>
            <p className="mt-4 text-sm text-gray-600">
              Professional mechanical engineering tools and calculators for engineers and designers.
            </p>
          </div>
          
          {/* Links */}
          {Object.entries(footerLinks).map(([category, links]) => (
            <div key={category}>
              <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase">
                {category}
              </h3>
              <ul className="mt-4 space-y-2">
                {links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-sm text-gray-600 hover:text-blue-600 transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
        
        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500 text-center">
            © {currentYear} MechWave. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

footer.displayName = 'Footer';

export { footer };
