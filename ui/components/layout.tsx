import React from 'react';
import { navbar } from './navbar';
import { footer } from './footer';

interface LayoutProps {
  children: React.ReactNode;
}

const layout = ({ children }: LayoutProps) => {
  return (
    <div className="min-h-screen flex flex-col">
      <navbar />
      <main className="flex-1">
        {children}
      </main>
      <footer />
    </div>
  );
};

layout.displayName = 'Layout';

export { layout };
export type { LayoutProps };
